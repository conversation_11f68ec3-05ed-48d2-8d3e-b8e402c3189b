// Shared types and interfaces for the AI Chat Interface

export interface Message {
  id: string;
  content: string;
  sender: 'user' | 'ai';
  timestamp: Date;
  isError?: boolean;
}

export interface ChatPreferences {
  autoScroll: boolean;
  soundNotifications: boolean;
  compactView: boolean;
  showTimestamps: boolean;
}

export interface Theme {
  mode: 'light' | 'dark';
  accentColor: string;
}

export interface ChatState {
  messages: Message[];
  isTyping: boolean;
  theme: Theme;
  preferences: ChatPreferences;
}

// Chat Actions for useReducer
export type ChatAction = 
  | { type: 'ADD_MESSAGE'; payload: Message }
  | { type: 'SET_TYPING'; payload: boolean }
  | { type: 'CLEAR_MESSAGES' }
  | { type: 'UPDATE_THEME'; payload: Theme }
  | { type: 'UPDATE_PREFERENCES'; payload: ChatPreferences }
  | { type: 'LOAD_STATE'; payload: Partial<ChatState> };

// Context type
export interface ChatContextType {
  state: ChatState;
  sendMessage: (content: string) => Promise<void>;
  clearHistory: () => void;
  updateTheme: (theme: Theme) => void;
  updatePreferences: (preferences: ChatPreferences) => void;
}

// Component Props Interfaces
export interface ChatPageProps {
  messages: Message[];
  onSendMessage: (content: string) => void;
  isTyping: boolean;
}

export interface MessageListProps {
  messages: Message[];
  isTyping: boolean;
}

export interface MessageInputProps {
  onSendMessage: (content: string) => void;
  disabled: boolean;
}

export interface MessageBubbleProps {
  message: Message;
  onCopy: () => void;
  onRegenerate?: () => void;
}

export interface SettingsPageProps {
  theme: Theme;
  preferences: ChatPreferences;
  onThemeChange: (theme: Theme) => void;
  onPreferencesChange: (preferences: ChatPreferences) => void;
  onClearHistory: () => void;
}

// Storage Keys
export const STORAGE_KEYS = {
  CHAT_HISTORY: 'ai-chat-history',
  THEME: 'ai-chat-theme',
  PREFERENCES: 'ai-chat-preferences'
} as const;