import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, Trash2 } from 'lucide-react';
import { Link } from 'react-router-dom';
import { MessageList } from '../components/MessageList';
import { MessageInput } from '../components/MessageInput';
import { useChat } from '../contexts/ChatContext';
import { toast } from 'sonner';

export function ChatPage() {
  const { state, sendMessage, clearHistory, updateTheme } = useChat();
  const { messages, isTyping, theme } = state;

  const handleSendMessage = async (content: string) => {
    await sendMessage(content);
  };

  const handleClearHistory = () => {
    if (messages.length === 0) {
      toast.info('No messages to clear');
      return;
    }

    if (window.confirm('Are you sure you want to clear all messages? This action cannot be undone.')) {
      clearHistory();
      toast.success('Chat history cleared');
    }
  };

  const toggleTheme = () => {
    const newMode = theme.mode === 'light' ? 'dark' : 'light';
    updateTheme({ ...theme, mode: newMode });
    toast.success(`Switched to ${newMode} mode`);
  };

  return (
    <div className="flex flex-col h-screen w-full bg-white dark:bg-gray-900 transition-colors duration-200">
      {/* Header */}
      <header className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-4 py-4 md:px-6 md:py-6">
        <div className="flex items-center justify-between">
          {/* Logo/Title */}
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
              <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
              </svg>
            </div>
            <div>
              <h1 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
                AI Chat Interface
              </h1>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {messages.length === 0 
                  ? 'Start a conversation' 
                  : `${messages.length} message${messages.length === 1 ? '' : 's'}`
                }
              </p>
            </div>
          </div>

          {/* Action buttons */}
          <div className="flex items-center space-x-2">
            {/* Clear history button */}
            <button
              onClick={handleClearHistory}
              disabled={messages.length === 0}
              className={`
                p-2 rounded-lg transition-all duration-200
                ${messages.length === 0
                  ? 'text-gray-400 dark:text-gray-600 cursor-not-allowed'
                  : 'text-gray-600 dark:text-gray-400 hover:text-red-600 dark:hover:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20'
                }
              `}
              title="Clear chat history"
            >
              <Trash2 className="w-5 h-5" />
            </button>

            {/* Theme toggle */}
            <button
              onClick={toggleTheme}
              className="p-2 rounded-lg text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 hover:bg-gray-100 dark:hover:bg-gray-700 transition-all duration-200"
              title={`Switch to ${theme.mode === 'light' ? 'dark' : 'light'} mode`}
            >
              {theme.mode === 'light' ? (
                <Moon className="w-5 h-5" />
              ) : (
                <Sun className="w-5 h-5" />
              )}
            </button>

            {/* Settings link */}
            <Link
              to="/settings"
              className="p-2 rounded-lg text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 hover:bg-gray-100 dark:hover:bg-gray-700 transition-all duration-200"
              title="Settings"
            >
              <Settings className="w-5 h-5" />
            </Link>
          </div>
        </div>

        {/* Status indicator */}
        {isTyping && (
          <div className="mt-3 flex items-center space-x-2 text-sm text-blue-600 dark:text-blue-400">
            <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
            <span>AI is thinking...</span>
          </div>
        )}
      </header>

      {/* Messages area */}
      <main className="flex-1 overflow-hidden">
        <MessageList messages={messages} isTyping={isTyping} />
      </main>

      {/* Input area */}
      <footer>
        <MessageInput 
          onSendMessage={handleSendMessage} 
          disabled={isTyping} 
        />
      </footer>
    </div>
  );
}