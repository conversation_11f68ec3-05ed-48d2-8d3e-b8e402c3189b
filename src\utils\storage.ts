import { Message, Theme, ChatPreferences, STORAGE_KEYS } from '../types';

/**
 * Service for handling local storage operations
 */
export class StorageService {
  /**
   * Save messages to local storage
   */
  static saveMessages(messages: Message[]): void {
    try {
      const serializedMessages = messages.map(msg => ({
        ...msg,
        timestamp: msg.timestamp.toISOString()
      }));
      localStorage.setItem(STORAGE_KEYS.CHAT_HISTORY, JSON.stringify(serializedMessages));
    } catch (error) {
      console.error('Failed to save messages to localStorage:', error);
    }
  }

  /**
   * Load messages from local storage
   */
  static loadMessages(): Message[] {
    try {
      const stored = localStorage.getItem(STORAGE_KEYS.CHAT_HISTORY);
      if (!stored) return [];
      
      const parsed = JSON.parse(stored);
      return parsed.map((msg: any) => ({
        ...msg,
        timestamp: new Date(msg.timestamp)
      }));
    } catch (error) {
      console.error('Failed to load messages from localStorage:', error);
      return [];
    }
  }

  /**
   * Save theme to local storage
   */
  static saveTheme(theme: Theme): void {
    try {
      localStorage.setItem(STORAGE_KEYS.THEME, JSON.stringify(theme));
    } catch (error) {
      console.error('Failed to save theme to localStorage:', error);
    }
  }

  /**
   * Load theme from local storage
   */
  static loadTheme(): Theme {
    try {
      const stored = localStorage.getItem(STORAGE_KEYS.THEME);
      return stored ? JSON.parse(stored) : { mode: 'light', accentColor: '#007bff' };
    } catch (error) {
      console.error('Failed to load theme from localStorage:', error);
      return { mode: 'light', accentColor: '#007bff' };
    }
  }

  /**
   * Save preferences to local storage
   */
  static savePreferences(preferences: ChatPreferences): void {
    try {
      localStorage.setItem(STORAGE_KEYS.PREFERENCES, JSON.stringify(preferences));
    } catch (error) {
      console.error('Failed to save preferences to localStorage:', error);
    }
  }

  /**
   * Load preferences from local storage
   */
  static loadPreferences(): ChatPreferences {
    try {
      const stored = localStorage.getItem(STORAGE_KEYS.PREFERENCES);
      return stored ? JSON.parse(stored) : {
        autoScroll: true,
        soundNotifications: false,
        compactView: false,
        showTimestamps: true
      };
    } catch (error) {
      console.error('Failed to load preferences from localStorage:', error);
      return {
        autoScroll: true,
        soundNotifications: false,
        compactView: false,
        showTimestamps: true
      };
    }
  }

  /**
   * Clear all stored data
   */
  static clearAll(): void {
    try {
      localStorage.removeItem(STORAGE_KEYS.CHAT_HISTORY);
      localStorage.removeItem(STORAGE_KEYS.THEME);
      localStorage.removeItem(STORAGE_KEYS.PREFERENCES);
    } catch (error) {
      console.error('Failed to clear localStorage:', error);
    }
  }

  /**
   * Export chat history as JSON
   */
  static exportChatHistory(): string {
    const messages = this.loadMessages();
    return JSON.stringify(messages, null, 2);
  }
}