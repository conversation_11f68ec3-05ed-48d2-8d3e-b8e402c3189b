import React, { useEffect, useRef } from 'react';
import { MessageListProps } from '../types';
import { MessageBubble } from './MessageBubble';
import { useChat } from '../contexts/ChatContext';
import { toast } from 'sonner';

// Typing indicator component
function TypingIndicator() {
  return (
    <div className="flex justify-start mb-4">
      <div className="max-w-[80%] md:max-w-[70%]">
        <div className="bg-gray-100 dark:bg-gray-700 px-4 py-3 rounded-lg shadow-sm">
          <div className="flex items-center space-x-1">
            <div className="text-gray-500 dark:text-gray-400 text-sm mr-2">AI is typing</div>
            <div className="flex space-x-1">
              <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
              <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
              <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Empty state component
function EmptyState() {
  return (
    <div className="flex flex-col items-center justify-center h-full text-center p-8">
      <div className="w-16 h-16 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center mb-4">
        <svg className="w-8 h-8 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
        </svg>
      </div>
      <h3 className="text-lg font-semibold text-gray-700 dark:text-gray-300 mb-2">
        Start a conversation
      </h3>
      <p className="text-gray-500 dark:text-gray-400 max-w-md">
        Send a message to begin chatting with the AI assistant. Ask questions, get help, or just have a friendly conversation!
      </p>
    </div>
  );
}

export function MessageList({ messages, isTyping }: MessageListProps) {
  const { sendMessage, state } = useChat();
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const { preferences } = state;

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (preferences.autoScroll && messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ 
        behavior: 'smooth',
        block: 'end'
      });
    }
  }, [messages, isTyping, preferences.autoScroll]);

  // Handle message copy
  const handleCopy = () => {
    if (preferences.soundNotifications) {
      // Play a subtle copy sound (if audio is enabled)
      // For now, just show a toast
    }
    toast.success('Message copied to clipboard');
  };

  // Handle message regeneration
  const handleRegenerate = async (messageIndex: number) => {
    const message = messages[messageIndex];
    if (message.sender === 'ai' && messageIndex > 0) {
      // Find the previous user message
      const userMessage = messages[messageIndex - 1];
      if (userMessage && userMessage.sender === 'user') {
        try {
          await sendMessage(userMessage.content);
          toast.success('Response regenerated');
        } catch (error) {
          toast.error('Failed to regenerate response');
        }
      }
    }
  };

  // Show empty state if no messages
  if (messages.length === 0 && !isTyping) {
    return <EmptyState />;
  }

  return (
    <div 
      ref={containerRef}
      className="flex-1 overflow-y-auto p-4 md:p-6 space-y-4"
      style={{ scrollBehavior: 'smooth' }}
    >
      {/* Messages */}
      {messages.map((message, index) => (
        <MessageBubble
          key={message.id}
          message={message}
          onCopy={handleCopy}
          onRegenerate={message.sender === 'ai' ? () => handleRegenerate(index) : undefined}
        />
      ))}

      {/* Typing indicator */}
      {isTyping && <TypingIndicator />}

      {/* Scroll anchor */}
      <div ref={messagesEndRef} />
    </div>
  );
}