import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { Toaster } from 'sonner';
import { ChatProvider } from './contexts/ChatContext';
import { ChatPage } from './pages/ChatPage';
import { SettingsPage } from './pages/SettingsPage';

export default function App() {
  return (
    <ChatProvider>
      <Router>
        <div className="min-h-screen">
          <Routes>
            <Route path="/" element={<ChatPage />} />
            <Route path="/settings" element={<SettingsPage />} />
          </Routes>
          <Toaster 
            position="top-right"
            richColors
            closeButton
            duration={3000}
          />
        </div>
      </Router>
    </ChatProvider>
  );
}
