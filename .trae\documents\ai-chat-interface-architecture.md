# AI Chat Interface - Technical Architecture Document

## 1. Architecture design

```mermaid
graph TD
  A[User Browser] --> B[React Frontend Application]
  B --> C[Local Storage]
  B --> D[Mock AI API / WebSocket]

  subgraph "Frontend Layer"
    B
    E[React Components]
    F[State Management]
    G[UI Components]
  end

  subgraph "Data Layer"
    C
    H[Session Storage]
  end

  subgraph "External Services (Mock)"
    D
  end
```

## 2. Technology Description

- Frontend: React@18 + TypeScript + Tailwind CSS@3 + Vite
- State Management: React Context API + useReducer
- Storage: Local Storage for chat history and preferences
- Mock Services: Simulated AI responses with setTimeout delays

## 3. Route definitions

| Route | Purpose |
|-------|----------|
| / | Main chat interface page with message display and input |
| /settings | Settings page for theme, preferences, and data management |

## 4. Component Structure

### 4.1 Core Components

```typescript
// Main App Component
interface AppProps {}

// Chat Interface Components
interface ChatPageProps {
  messages: Message[];
  onSendMessage: (content: string) => void;
  isTyping: boolean;
}

interface MessageListProps {
  messages: Message[];
  isTyping: boolean;
}

interface MessageInputProps {
  onSendMessage: (content: string) => void;
  disabled: boolean;
}

interface MessageBubbleProps {
  message: Message;
  onCopy: () => void;
  onRegenerate?: () => void;
}

// Settings Components
interface SettingsPageProps {
  theme: Theme;
  preferences: ChatPreferences;
  onThemeChange: (theme: Theme) => void;
  onPreferencesChange: (preferences: ChatPreferences) => void;
  onClearHistory: () => void;
}
```

### 4.2 Data Types

```typescript
interface Message {
  id: string;
  content: string;
  sender: 'user' | 'ai';
  timestamp: Date;
  isError?: boolean;
}

interface ChatPreferences {
  autoScroll: boolean;
  soundNotifications: boolean;
  compactView: boolean;
  showTimestamps: boolean;
}

interface Theme {
  mode: 'light' | 'dark';
  accentColor: string;
}

interface ChatState {
  messages: Message[];
  isTyping: boolean;
  theme: Theme;
  preferences: ChatPreferences;
}
```

## 5. Mock AI Service

### 5.1 Simulated API Responses

```typescript
// Mock AI Service
class MockAIService {
  async generateResponse(userMessage: string): Promise<string> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));
    
    // Return mock responses based on input
    const responses = [
      "That's an interesting question! Let me think about that...",
      "I understand what you're asking. Here's my perspective...",
      "Based on what you've shared, I would suggest...",
      "That's a great point. Let me elaborate on that..."
    ];
    
    return responses[Math.floor(Math.random() * responses.length)];
  }
}
```

## 6. State Management

### 6.1 Chat Context

```typescript
interface ChatContextType {
  state: ChatState;
  sendMessage: (content: string) => Promise<void>;
  clearHistory: () => void;
  updateTheme: (theme: Theme) => void;
  updatePreferences: (preferences: ChatPreferences) => void;
}

// Chat Actions
type ChatAction = 
  | { type: 'ADD_MESSAGE'; payload: Message }
  | { type: 'SET_TYPING'; payload: boolean }
  | { type: 'CLEAR_MESSAGES' }
  | { type: 'UPDATE_THEME'; payload: Theme }
  | { type: 'UPDATE_PREFERENCES'; payload: ChatPreferences }
  | { type: 'LOAD_STATE'; payload: Partial<ChatState> };
```

## 7. Local Storage Schema

### 7.1 Data Persistence

```typescript
// Local Storage Keys
const STORAGE_KEYS = {
  CHAT_HISTORY: 'ai-chat-history',
  THEME: 'ai-chat-theme',
  PREFERENCES: 'ai-chat-preferences'
} as const;

// Storage Service
class StorageService {
  static saveMessages(messages: Message[]): void {
    localStorage.setItem(STORAGE_KEYS.CHAT_HISTORY, JSON.stringify(messages));
  }
  
  static loadMessages(): Message[] {
    const stored = localStorage.getItem(STORAGE_KEYS.CHAT_HISTORY);
    return stored ? JSON.parse(stored) : [];
  }
  
  static saveTheme(theme: Theme): void {
    localStorage.setItem(STORAGE_KEYS.THEME, JSON.stringify(theme));
  }
  
  static loadTheme(): Theme {
    const stored = localStorage.getItem(STORAGE_KEYS.THEME);
    return stored ? JSON.parse(stored) : { mode: 'light', accentColor: '#007bff' };
  }
}
```

## 8. Responsive Design Implementation

### 8.1 Tailwind CSS Breakpoints

- Mobile: Default (< 768px)
- Tablet: md: (768px - 1024px)
- Desktop: lg: (> 1024px)

### 8.2 Component Responsiveness

```typescript
// Responsive Chat Layout
const ChatLayout = () => {
  return (
    <div className="flex flex-col h-screen max-w-4xl mx-auto">
      {/* Header */}
      <header className="bg-white dark:bg-gray-800 border-b p-4 md:p-6">
        {/* Header content */}
      </header>
      
      {/* Messages */}
      <main className="flex-1 overflow-y-auto p-4 md:p-6">
        {/* Message list */}
      </main>
      
      {/* Input */}
      <footer className="bg-white dark:bg-gray-800 border-t p-4 md:p-6">
        {/* Message input */}
      </footer>
    </div>
  );
};
```