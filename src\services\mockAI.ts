/**
 * Mock AI Service for simulating AI responses
 * Provides realistic delays and varied responses for testing
 */
export class MockAIService {
  private static responses = [
    "That's an interesting question! Let me think about that...",
    "I understand what you're asking. Here's my perspective...",
    "Based on what you've shared, I would suggest...",
    "That's a great point. Let me elaborate on that...",
    "I see what you mean. Here's how I would approach this...",
    "Interesting! I have some thoughts on this topic...",
    "Let me break this down for you...",
    "That's a thoughtful question. Here's what I think...",
    "I appreciate you asking about this. My take is...",
    "Good question! This reminds me of..."
  ];

  private static contextualResponses: Record<string, string[]> = {
    greeting: [
      "Hello! I'm here to help you with any questions you might have.",
      "Hi there! What can I assist you with today?",
      "Greetings! I'm ready to chat whenever you are.",
      "Hello! Feel free to ask me anything you'd like to know."
    ],
    question: [
      "That's a great question! Let me provide you with a detailed answer...",
      "I'm glad you asked about this. Here's what I know...",
      "Excellent question! This is actually quite interesting...",
      "Good point! Let me explain this in detail..."
    ],
    thanks: [
      "You're very welcome! I'm happy I could help.",
      "My pleasure! Feel free to ask if you have any other questions.",
      "Glad I could assist! Is there anything else you'd like to know?",
      "You're welcome! I'm here whenever you need help."
    ],
    goodbye: [
      "Goodbye! It was great chatting with you.",
      "See you later! Feel free to come back anytime.",
      "Take care! I'll be here if you need me again.",
      "Farewell! Thanks for the interesting conversation."
    ]
  };

  /**
   * Generate a response based on user input
   * Simulates API delay and provides contextual responses
   */
  static async generateResponse(userMessage: string): Promise<string> {
    // Simulate realistic API delay (1-3 seconds)
    const delay = 1000 + Math.random() * 2000;
    await new Promise(resolve => setTimeout(resolve, delay));

    // Simulate occasional errors (5% chance)
    if (Math.random() < 0.05) {
      throw new Error('AI service temporarily unavailable. Please try again.');
    }

    const message = userMessage.toLowerCase().trim();
    
    // Contextual response selection
    if (this.isGreeting(message)) {
      return this.getRandomResponse('greeting');
    }
    
    if (this.isQuestion(message)) {
      return this.getRandomResponse('question');
    }
    
    if (this.isThanks(message)) {
      return this.getRandomResponse('thanks');
    }
    
    if (this.isGoodbye(message)) {
      return this.getRandomResponse('goodbye');
    }

    // Default random response
    return this.responses[Math.floor(Math.random() * this.responses.length)];
  }

  /**
   * Check if message is a greeting
   */
  private static isGreeting(message: string): boolean {
    const greetings = ['hello', 'hi', 'hey', 'greetings', 'good morning', 'good afternoon', 'good evening'];
    return greetings.some(greeting => message.includes(greeting));
  }

  /**
   * Check if message is a question
   */
  private static isQuestion(message: string): boolean {
    const questionWords = ['what', 'how', 'why', 'when', 'where', 'who', 'which', 'can you', 'could you', 'would you'];
    return message.includes('?') || questionWords.some(word => message.includes(word));
  }

  /**
   * Check if message is expressing thanks
   */
  private static isThanks(message: string): boolean {
    const thanksWords = ['thank', 'thanks', 'appreciate', 'grateful'];
    return thanksWords.some(word => message.includes(word));
  }

  /**
   * Check if message is a goodbye
   */
  private static isGoodbye(message: string): boolean {
    const goodbyes = ['bye', 'goodbye', 'see you', 'farewell', 'take care', 'later'];
    return goodbyes.some(goodbye => message.includes(goodbye));
  }

  /**
   * Get random response from specific category
   */
  private static getRandomResponse(category: keyof typeof MockAIService.contextualResponses): string {
    const responses = this.contextualResponses[category];
    return responses[Math.floor(Math.random() * responses.length)];
  }

  /**
   * Simulate typing delay for real-time effect
   */
  static getTypingDelay(): number {
    return 500 + Math.random() * 1000; // 0.5-1.5 seconds
  }
}