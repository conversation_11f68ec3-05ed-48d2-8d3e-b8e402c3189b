import React, { useState } from 'react';
import { Co<PERSON>, RotateCcw, Check } from 'lucide-react';
import { MessageBubbleProps } from '../types';
import { useChat } from '../contexts/ChatContext';

export function MessageBubble({ message, onCopy, onRegenerate }: MessageBubbleProps) {
  const { state } = useChat();
  const [copied, setCopied] = useState(false);
  const { preferences } = state;

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(message.content);
      setCopied(true);
      onCopy();
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error('Failed to copy message:', error);
    }
  };

  const formatTimestamp = (timestamp: Date) => {
    return timestamp.toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  const isUser = message.sender === 'user';
  const isError = message.isError;

  return (
    <div className={`flex ${isUser ? 'justify-end' : 'justify-start'} mb-4 group`}>
      <div className={`max-w-[80%] md:max-w-[70%] ${preferences.compactView ? 'mb-2' : 'mb-4'}`}>
        {/* Message bubble */}
        <div
          className={`
            relative px-4 py-3 rounded-lg shadow-sm
            ${isUser 
              ? 'bg-blue-500 text-white ml-auto' 
              : isError 
                ? 'bg-red-50 border border-red-200 text-red-800 dark:bg-red-900/20 dark:border-red-800 dark:text-red-200'
                : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'
            }
            ${preferences.compactView ? 'text-sm py-2' : 'text-base'}
            transition-all duration-200 hover:shadow-md
          `}
        >
          {/* Message content */}
          <div className="whitespace-pre-wrap break-words">
            {message.content}
          </div>

          {/* Action buttons */}
          <div className={`
            absolute top-2 ${isUser ? 'left-2' : 'right-2'}
            opacity-0 group-hover:opacity-100 transition-opacity duration-200
            flex gap-1
          `}>
            {/* Copy button */}
            <button
              onClick={handleCopy}
              className={`
                p-1 rounded hover:bg-black/10 dark:hover:bg-white/10
                transition-colors duration-200
                ${isUser ? 'text-white/70 hover:text-white' : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200'}
              `}
              title="Copy message"
            >
              {copied ? (
                <Check className="w-3 h-3" />
              ) : (
                <Copy className="w-3 h-3" />
              )}
            </button>

            {/* Regenerate button (only for AI messages) */}
            {!isUser && onRegenerate && (
              <button
                onClick={onRegenerate}
                className="
                  p-1 rounded hover:bg-black/10 dark:hover:bg-white/10
                  transition-colors duration-200
                  text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200
                "
                title="Regenerate response"
              >
                <RotateCcw className="w-3 h-3" />
              </button>
            )}
          </div>
        </div>

        {/* Timestamp */}
        {preferences.showTimestamps && (
          <div className={`
            text-xs text-gray-500 dark:text-gray-400 mt-1
            ${isUser ? 'text-right' : 'text-left'}
            ${preferences.compactView ? 'text-xs' : 'text-sm'}
          `}>
            {formatTimestamp(message.timestamp)}
          </div>
        )}
      </div>
    </div>
  );
}