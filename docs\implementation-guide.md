# Game-Master AI Engine - Implementation Guide

## Quick Start Implementation

This guide provides step-by-step instructions to implement the Game-Master AI Engine Backend described in the technical documentation.

## 1. Project Setup

### Initialize Backend Project

```bash
# Create backend directory
mkdir backend
cd backend

# Initialize Node.js project
npm init -y

# Install core dependencies
npm install express typescript ts-node @types/node @types/express
npm install openai socket.io cors helmet morgan
npm install redis ioredis mongodb mongoose
npm install joi jsonwebtoken bcryptjs
npm install express-rate-limit winston

# Install development dependencies
npm install -D nodemon @types/cors @types/jsonwebtoken
npm install -D @types/bcryptjs @types/joi jest @types/jest
npm install -D supertest @types/supertest
```

### TypeScript Configuration

```json
// tsconfig.json
{
  "compilerOptions": {
    "target": "ES2020",
    "module": "commonjs",
    "lib": ["ES2020"],
    "outDir": "./dist",
    "rootDir": "./src",
    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "resolveJsonModule": true,
    "declaration": true,
    "declarationMap": true,
    "sourceMap": true
  },
  "include": ["src/**/*"],
  "exclude": ["node_modules", "dist", "tests"]
}
```

### Package.json Scripts

```json
{
  "scripts": {
    "dev": "nodemon src/app.ts",
    "build": "tsc",
    "start": "node dist/app.js",
    "test": "jest",
    "test:watch": "jest --watch"
  }
}
```

## 2. Basic Server Setup

### Main Application File

```typescript
// src/app.ts
import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import { createServer } from 'http';
import { Server } from 'socket.io';
import { gameRoutes } from './routes/gameRoutes';
import { errorHandler } from './middleware/errorHandler';
import { connectDatabase } from './config/database';
import { SocketHandler } from './services/socketHandler';

const app = express();
const server = createServer(app);
const io = new Server(server, {
  cors: {
    origin: process.env.FRONTEND_URL || "http://localhost:3000",
    methods: ["GET", "POST"]
  }
});

// Middleware
app.use(helmet());
app.use(cors());
app.use(morgan('combined'));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Routes
app.use('/api/game', gameRoutes);

// Socket.io setup
new SocketHandler(io);

// Error handling
app.use(errorHandler);

// Database connection
connectDatabase();

const PORT = process.env.PORT || 3001;
server.listen(PORT, () => {
  console.log(`Game Master AI Engine running on port ${PORT}`);
});
```

## 3. Core Types and Interfaces

```typescript
// src/types/index.ts
export interface GameState {
  sessionId: string;
  gameId: string;
  currentScene: Scene;
  characters: Character[];
  playerState: PlayerState;
  worldState: WorldState;
  actionHistory: GameAction[];
  timestamp: Date;
}

export interface Character {
  id: string;
  name: string;
  personality: string;
  background: string;
  currentMood: string;
  relationships: Record<string, number>;
  isActive: boolean;
  lastSpoken: Date;
}

export interface Scene {
  id: string;
  name: string;
  description: string;
  availableActions: string[];
  activeCharacters: string[];
  environment: EnvironmentState;
}

export interface GameAction {
  sessionId: string;
  action: string;
  target?: string;
  parameters?: Record<string, any>;
  timestamp: Date;
}

export interface GameResponse {
  narrativeDescription: string;
  characterResponses: CharacterResponse[];
  gameState: GameState;
  availableActions: string[];
}

export interface CharacterResponse {
  characterId: string;
  dialogue: string;
  action: string;
  emotion: string;
  targetCharacter?: string;
}

export interface PlayerState {
  name: string;
  level: number;
  health: number;
  inventory: string[];
  location: string;
  status: string[];
}

export interface WorldState {
  timeOfDay: string;
  weather: string;
  globalEvents: string[];
  locationStates: Record<string, any>;
}

export interface EnvironmentState {
  lighting: string;
  temperature: string;
  sounds: string[];
  objects: string[];
  exits: string[];
}
```

## 4. Environment Configuration

```bash
# .env
PORT=3001
NODE_ENV=development

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4

# Database
MONGODB_URI=mongodb://localhost:27017/gamemaster
REDIS_URL=redis://localhost:6379

# Security
JWT_SECRET=your_super_secret_jwt_key_here
BCRYPT_ROUNDS=12

# CORS
FRONTEND_URL=http://localhost:3000

# Rate Limiting
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=30

# Logging
LOG_LEVEL=info
```

## 5. Basic Route Setup

```typescript
// src/routes/gameRoutes.ts
import { Router } from 'express';
import { GameController } from '../controllers/gameController';
import { gameActionLimiter } from '../middleware/rateLimiter';
import { validateGameAction } from '../middleware/validation';

const router = Router();
const gameController = new GameController();

// Game session management
router.post('/create', gameController.createGame.bind(gameController));
router.get('/:sessionId/state', gameController.getGameState.bind(gameController));

// Game actions
router.post(
  '/:sessionId/action',
  gameActionLimiter,
  validateGameAction,
  gameController.processAction.bind(gameController)
);

// Character interaction
router.post(
  '/:sessionId/speak',
  gameActionLimiter,
  gameController.speakToCharacter.bind(gameController)
);

router.get('/:sessionId/characters', gameController.getCharacters.bind(gameController));

export { router as gameRoutes };
```

## 6. Minimal AI Service Implementation

```typescript
// src/services/aiService.ts
import OpenAI from 'openai';
import { GameState, GameAction, AIResponse } from '../types';

export class AIService {
  private openai: OpenAI;

  constructor() {
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY
    });
  }

  async processGameAction(userInput: string, gameState: GameState): Promise<any> {
    const systemPrompt = this.buildSystemPrompt(gameState);
    const userPrompt = `Player action: ${userInput}`;

    try {
      const completion = await this.openai.chat.completions.create({
        model: process.env.OPENAI_MODEL || "gpt-4",
        messages: [
          { role: "system", content: systemPrompt },
          { role: "user", content: userPrompt }
        ],
        temperature: 0.8,
        max_tokens: 1000
      });

      return this.parseResponse(completion.choices[0].message.content || "");
    } catch (error) {
      throw new Error(`AI processing failed: ${error.message}`);
    }
  }

  private buildSystemPrompt(gameState: GameState): string {
    return `You are a Game Master for an interactive role-playing game.
    
Current Scene: ${gameState.currentScene.name}
Scene Description: ${gameState.currentScene.description}
Active Characters: ${gameState.characters.map(c => c.name).join(', ')}
Player Location: ${gameState.playerState.location}

Your role:
1. Interpret player actions and determine consequences
2. Control NPC characters with distinct personalities
3. Update the game world state
4. Provide engaging narrative descriptions

Respond in JSON format with:
{
  "narrativeDescription": "What happens as a result of the action",
  "characterResponses": [
    {
      "characterId": "character_id",
      "dialogue": "What the character says",
      "action": "What the character does",
      "emotion": "Character's emotional state"
    }
  ],
  "stateChanges": {
    "sceneChanges": {},
    "characterUpdates": [],
    "worldStateChanges": {}
  },
  "availableActions": ["list", "of", "available", "actions"]
}`;
  }

  private parseResponse(response: string): any {
    try {
      return JSON.parse(response);
    } catch (error) {
      // Fallback if AI doesn't return valid JSON
      return {
        narrativeDescription: response,
        characterResponses: [],
        stateChanges: {},
        availableActions: ["continue", "look around", "speak"]
      };
    }
  }
}
```

## 7. Testing the Implementation

### Basic Test Setup

```typescript
// tests/gameEngine.test.ts
import { GameEngine } from '../src/services/gameEngine';

describe('GameEngine', () => {
  let gameEngine: GameEngine;

  beforeEach(() => {
    gameEngine = new GameEngine();
  });

  test('should create a new game session', async () => {
    const gameParams = {
      gameType: 'fantasy',
      playerName: 'TestPlayer',
      difficulty: 'medium' as const
    };

    const session = await gameEngine.createGame(gameParams);
    
    expect(session).toHaveProperty('sessionId');
    expect(session).toHaveProperty('initialState');
    expect(session.initialState.playerState.name).toBe('TestPlayer');
  });
});
```

## 8. Running the Application

```bash
# Development mode
npm run dev

# Production build
npm run build
npm start

# Run tests
npm test
```

## 9. Integration with Frontend

The backend provides RESTful APIs and WebSocket connections that can be integrated with your existing React frontend. Update your frontend to connect to the backend:

```typescript
// Frontend integration example
const API_BASE = 'http://localhost:3001/api';

export const gameAPI = {
  createGame: async (params) => {
    const response = await fetch(`${API_BASE}/game/create`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(params)
    });
    return response.json();
  },
  
  sendAction: async (sessionId, action) => {
    const response = await fetch(`${API_BASE}/game/${sessionId}/action`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ action })
    });
    return response.json();
  }
};
```

This implementation guide provides the foundation for building your Game-Master AI Engine. You can extend it with additional features like persistent storage, advanced character AI, and more sophisticated game mechanics as needed.
