import React, { useState, useRef, useEffect } from 'react';
import { Send, Loader2 } from 'lucide-react';
import { MessageInputProps } from '../types';
import { useChat } from '../contexts/ChatContext';

export function MessageInput({ onSendMessage, disabled }: MessageInputProps) {
  const [message, setMessage] = useState('');
  const [isComposing, setIsComposing] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const { state } = useChat();
  const { preferences } = state;

  const maxLength = 2000;
  const isNearLimit = message.length > maxLength * 0.8;
  const isOverLimit = message.length > maxLength;

  // Auto-resize textarea
  useEffect(() => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = 'auto';
      const scrollHeight = textarea.scrollHeight;
      const maxHeight = 120; // Max height in pixels
      textarea.style.height = `${Math.min(scrollHeight, maxHeight)}px`;
    }
  }, [message]);

  // Focus textarea on mount
  useEffect(() => {
    if (textareaRef.current && !disabled) {
      textareaRef.current.focus();
    }
  }, [disabled]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    await sendMessage();
  };

  const sendMessage = async () => {
    if (!message.trim() || disabled || isOverLimit) return;

    const messageToSend = message.trim();
    setMessage('');
    
    try {
      await onSendMessage(messageToSend);
    } catch (error) {
      // If sending fails, restore the message
      setMessage(messageToSend);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    // Handle composition events (for IME input)
    if (isComposing) return;

    // Send on Enter (but not Shift+Enter)
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }

    // Clear on Escape
    if (e.key === 'Escape') {
      setMessage('');
    }
  };

  const handleCompositionStart = () => {
    setIsComposing(true);
  };

  const handleCompositionEnd = () => {
    setIsComposing(false);
  };

  const handlePaste = (e: React.ClipboardEvent<HTMLTextAreaElement>) => {
    const pastedText = e.clipboardData.getData('text');
    const newLength = message.length + pastedText.length;
    
    if (newLength > maxLength) {
      e.preventDefault();
      const remainingSpace = maxLength - message.length;
      if (remainingSpace > 0) {
        const truncatedText = pastedText.substring(0, remainingSpace);
        setMessage(prev => prev + truncatedText);
      }
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 p-4 md:p-6">
      <form onSubmit={handleSubmit} className="flex flex-col space-y-3">
        {/* Character counter */}
        {(isNearLimit || isOverLimit) && (
          <div className={`text-sm text-right ${
            isOverLimit 
              ? 'text-red-500 dark:text-red-400' 
              : 'text-yellow-600 dark:text-yellow-400'
          }`}>
            {message.length}/{maxLength}
          </div>
        )}

        {/* Input area */}
        <div className="flex items-end space-x-3">
          {/* Textarea */}
          <div className="flex-1 relative">
            <textarea
              ref={textareaRef}
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              onKeyDown={handleKeyDown}
              onCompositionStart={handleCompositionStart}
              onCompositionEnd={handleCompositionEnd}
              onPaste={handlePaste}
              placeholder="Type your message... (Press Enter to send, Shift+Enter for new line)"
              disabled={disabled}
              maxLength={maxLength}
              className={`
                w-full px-4 py-3 rounded-lg border resize-none
                bg-gray-50 dark:bg-gray-700 
                border-gray-300 dark:border-gray-600
                text-gray-900 dark:text-gray-100
                placeholder-gray-500 dark:placeholder-gray-400
                focus:ring-2 focus:ring-blue-500 focus:border-transparent
                disabled:opacity-50 disabled:cursor-not-allowed
                transition-all duration-200
                ${isOverLimit ? 'border-red-500 focus:ring-red-500' : ''}
                ${preferences.compactView ? 'text-sm py-2' : 'text-base py-3'}
              `}
              rows={1}
              style={{ minHeight: preferences.compactView ? '36px' : '44px' }}
            />
            
            {/* Character limit warning overlay */}
            {isOverLimit && (
              <div className="absolute inset-0 bg-red-50/50 dark:bg-red-900/20 rounded-lg pointer-events-none" />
            )}
          </div>

          {/* Send button */}
          <button
            type="submit"
            disabled={disabled || !message.trim() || isOverLimit}
            className={`
              p-3 rounded-lg transition-all duration-200
              ${disabled || !message.trim() || isOverLimit
                ? 'bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400 cursor-not-allowed'
                : 'bg-blue-500 hover:bg-blue-600 text-white shadow-sm hover:shadow-md active:scale-95'
              }
              ${preferences.compactView ? 'p-2' : 'p-3'}
            `}
            title={isOverLimit ? 'Message too long' : 'Send message (Enter)'}
          >
            {disabled ? (
              <Loader2 className={`${preferences.compactView ? 'w-4 h-4' : 'w-5 h-5'} animate-spin`} />
            ) : (
              <Send className={`${preferences.compactView ? 'w-4 h-4' : 'w-5 h-5'}`} />
            )}
          </button>
        </div>

        {/* Keyboard shortcuts hint */}
        <div className="text-xs text-gray-500 dark:text-gray-400 text-center">
          Press <kbd className="px-1 py-0.5 bg-gray-200 dark:bg-gray-600 rounded text-xs">Enter</kbd> to send, 
          <kbd className="px-1 py-0.5 bg-gray-200 dark:bg-gray-600 rounded text-xs">Shift+Enter</kbd> for new line
        </div>
      </form>
    </div>
  );
}