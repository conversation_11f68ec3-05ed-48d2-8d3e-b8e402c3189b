import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import { ChatState, ChatAction, ChatContextType, Message, Theme, ChatPreferences } from '../types';
import { StorageService } from '../utils/storage';
import { MockAIService } from '../services/mockAI';

// Initial state
const initialState: ChatState = {
  messages: [],
  isTyping: false,
  theme: { mode: 'light', accentColor: '#007bff' },
  preferences: {
    autoScroll: true,
    soundNotifications: false,
    compactView: false,
    showTimestamps: true
  }
};

// Reducer function
function chatReducer(state: ChatState, action: ChatAction): ChatState {
  switch (action.type) {
    case 'ADD_MESSAGE':
      return {
        ...state,
        messages: [...state.messages, action.payload]
      };
    
    case 'SET_TYPING':
      return {
        ...state,
        isTyping: action.payload
      };
    
    case 'CLEAR_MESSAGES':
      return {
        ...state,
        messages: []
      };
    
    case 'UPDATE_THEME':
      return {
        ...state,
        theme: action.payload
      };
    
    case 'UPDATE_PREFERENCES':
      return {
        ...state,
        preferences: action.payload
      };
    
    case 'LOAD_STATE':
      return {
        ...state,
        ...action.payload
      };
    
    default:
      return state;
  }
}

// Create context
const ChatContext = createContext<ChatContextType | undefined>(undefined);

// Provider component
interface ChatProviderProps {
  children: ReactNode;
}

export function ChatProvider({ children }: ChatProviderProps) {
  const [state, dispatch] = useReducer(chatReducer, initialState);

  // Load initial state from localStorage
  useEffect(() => {
    const loadInitialState = () => {
      const messages = StorageService.loadMessages();
      const theme = StorageService.loadTheme();
      const preferences = StorageService.loadPreferences();
      
      dispatch({
        type: 'LOAD_STATE',
        payload: { messages, theme, preferences }
      });
    };

    loadInitialState();
  }, []);

  // Save state changes to localStorage
  useEffect(() => {
    StorageService.saveMessages(state.messages);
  }, [state.messages]);

  useEffect(() => {
    StorageService.saveTheme(state.theme);
    // Apply theme to document
    document.documentElement.classList.toggle('dark', state.theme.mode === 'dark');
    document.documentElement.style.setProperty('--accent-color', state.theme.accentColor);
  }, [state.theme]);

  useEffect(() => {
    StorageService.savePreferences(state.preferences);
  }, [state.preferences]);

  // Send message function
  const sendMessage = async (content: string): Promise<void> => {
    if (!content.trim()) return;

    // Add user message
    const userMessage: Message = {
      id: `user-${Date.now()}`,
      content: content.trim(),
      sender: 'user',
      timestamp: new Date()
    };

    dispatch({ type: 'ADD_MESSAGE', payload: userMessage });
    dispatch({ type: 'SET_TYPING', payload: true });

    try {
      // Get AI response
      const aiResponse = await MockAIService.generateResponse(content);
      
      const aiMessage: Message = {
        id: `ai-${Date.now()}`,
        content: aiResponse,
        sender: 'ai',
        timestamp: new Date()
      };

      dispatch({ type: 'ADD_MESSAGE', payload: aiMessage });
    } catch (error) {
      // Add error message
      const errorMessage: Message = {
        id: `error-${Date.now()}`,
        content: error instanceof Error ? error.message : 'An error occurred while generating response.',
        sender: 'ai',
        timestamp: new Date(),
        isError: true
      };

      dispatch({ type: 'ADD_MESSAGE', payload: errorMessage });
    } finally {
      dispatch({ type: 'SET_TYPING', payload: false });
    }
  };

  // Clear chat history
  const clearHistory = (): void => {
    dispatch({ type: 'CLEAR_MESSAGES' });
    StorageService.saveMessages([]);
  };

  // Update theme
  const updateTheme = (theme: Theme): void => {
    dispatch({ type: 'UPDATE_THEME', payload: theme });
  };

  // Update preferences
  const updatePreferences = (preferences: ChatPreferences): void => {
    dispatch({ type: 'UPDATE_PREFERENCES', payload: preferences });
  };

  const contextValue: ChatContextType = {
    state,
    sendMessage,
    clearHistory,
    updateTheme,
    updatePreferences
  };

  return (
    <ChatContext.Provider value={contextValue}>
      {children}
    </ChatContext.Provider>
  );
}

// Custom hook to use chat context
export function useChat(): ChatContextType {
  const context = useContext(ChatContext);
  if (context === undefined) {
    throw new Error('useChat must be used within a ChatProvider');
  }
  return context;
}

export { ChatContext };