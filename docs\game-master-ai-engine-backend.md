# Game-Master AI Engine Backend - Technical Documentation

## 1. Architecture Overview

The Game-Master AI Engine Backend is a sophisticated Express.js-powered system that leverages the OpenAI SDK to create dynamic, multi-character game experiences. The engine interprets user actions, manages game state, and generates contextual responses from multiple characters including the Game Master.

```mermaid
graph TD
    A[User Input] --> B[Express.js API Gateway]
    B --> C[Input Parser & Validator]
    C --> D[Game State Manager]
    D --> E[OpenAI AI Engine]
    E --> F[Character Response Generator]
    F --> G[Game State Updater]
    G --> H[Response Formatter]
    H --> I[Client Response]
    
    subgraph "Core Components"
        J[Game Session Manager]
        K[Character Database]
        L[Action Interpreter]
        M[Context Builder]
    end
    
    subgraph "External Services"
        N[OpenAI GPT-4]
        O[Redis Cache]
        P[MongoDB/PostgreSQL]
    end
    
    D --> J
    E --> N
    F --> K
    C --> L
    E --> M
    J --> O
    D --> P
```

## 2. Technology Stack

### Backend Framework
- **Express.js 4.x**: RESTful API server with middleware support
- **TypeScript**: Type-safe development with enhanced IDE support
- **Node.js 18+**: Runtime environment with ES2022 features

### AI Integration
- **OpenAI SDK**: GPT-4 integration for natural language processing
- **Custom Prompt Engineering**: Specialized prompts for game scenarios
- **Context Management**: Conversation history and game state tracking

### Data Storage
- **Redis**: Session management and real-time game state caching
- **MongoDB/PostgreSQL**: Persistent game data, character profiles, and history
- **In-Memory Store**: Temporary action queues and real-time updates

### Additional Libraries
- **Socket.io**: Real-time bidirectional communication
- **Joi/Zod**: Input validation and schema enforcement
- **Winston**: Structured logging and error tracking
- **Rate Limiting**: API protection and usage management

## 3. Core System Components

### 3.1 Game State Manager

```typescript
interface GameState {
  sessionId: string;
  gameId: string;
  currentScene: Scene;
  characters: Character[];
  playerState: PlayerState;
  worldState: WorldState;
  actionHistory: GameAction[];
  timestamp: Date;
}

interface Scene {
  id: string;
  name: string;
  description: string;
  availableActions: string[];
  activeCharacters: string[];
  environment: EnvironmentState;
}

interface Character {
  id: string;
  name: string;
  personality: string;
  background: string;
  currentMood: string;
  relationships: Record<string, number>;
  isActive: boolean;
  lastSpoken: Date;
}
```

### 3.2 AI Engine Integration

```typescript
class GameMasterAI {
  private openai: OpenAI;
  private contextBuilder: ContextBuilder;
  
  async interpretAction(
    userInput: string, 
    gameState: GameState
  ): Promise<GameResponse> {
    const context = this.contextBuilder.buildContext(gameState);
    const prompt = this.createGameMasterPrompt(userInput, context);
    
    const response = await this.openai.chat.completions.create({
      model: "gpt-4",
      messages: [
        { role: "system", content: this.getSystemPrompt() },
        { role: "user", content: prompt }
      ],
      functions: this.getGameFunctions(),
      temperature: 0.8,
      max_tokens: 1000
    });
    
    return this.parseAIResponse(response);
  }
}
```

### 3.3 Character Response System

```typescript
interface CharacterResponse {
  characterId: string;
  dialogue: string;
  action: string;
  emotion: string;
  targetCharacter?: string;
}

class MultiCharacterEngine {
  async generateCharacterResponses(
    gameAction: GameAction,
    activeCharacters: Character[],
    gameState: GameState
  ): Promise<CharacterResponse[]> {
    const responses: CharacterResponse[] = [];
    
    for (const character of activeCharacters) {
      if (this.shouldCharacterRespond(character, gameAction)) {
        const response = await this.generateCharacterResponse(
          character, 
          gameAction, 
          gameState
        );
        responses.push(response);
      }
    }
    
    return this.orderResponsesByPriority(responses);
  }
}
```

## 4. API Endpoints

### 4.1 Game Session Management

```typescript
// POST /api/game/create
interface CreateGameRequest {
  gameType: string;
  playerName: string;
  difficulty: 'easy' | 'medium' | 'hard';
  customSettings?: GameSettings;
}

// POST /api/game/:sessionId/action
interface GameActionRequest {
  action: string;
  target?: string;
  parameters?: Record<string, any>;
}

// GET /api/game/:sessionId/state
interface GameStateResponse {
  gameState: GameState;
  availableActions: string[];
  characterStates: Character[];
  sceneDescription: string;
}
```

### 4.2 Character Interaction

```typescript
// POST /api/game/:sessionId/speak
interface SpeakToCharacterRequest {
  characterId: string;
  message: string;
  tone?: 'friendly' | 'aggressive' | 'neutral' | 'persuasive';
}

// GET /api/game/:sessionId/characters
interface CharacterListResponse {
  characters: Character[];
  relationships: Record<string, Record<string, number>>;
}
```

## 5. OpenAI Integration Strategy

### 5.1 System Prompts

```typescript
const GAME_MASTER_SYSTEM_PROMPT = `
You are an expert Game Master for a dynamic role-playing game. Your responsibilities:

1. Interpret player actions and determine their consequences
2. Manage the game world state and narrative progression
3. Control non-player characters (NPCs) with distinct personalities
4. Maintain consistency in the game world and character behaviors
5. Create engaging, challenging, and fair gameplay experiences

Response Format:
- Always respond in valid JSON format
- Include game state updates, character responses, and narrative descriptions
- Maintain character consistency and world logic
- Provide multiple interaction options for the player

Current Game Context: {gameContext}
Active Characters: {activeCharacters}
Current Scene: {currentScene}
`;

const CHARACTER_RESPONSE_PROMPT = `
You are {characterName}, a character in an ongoing role-playing game.

Character Profile:
- Personality: {personality}
- Background: {background}
- Current Mood: {currentMood}
- Relationships: {relationships}

Current Situation: {currentSituation}
Player Action: {playerAction}

Respond as this character would, considering:
1. Your personality and background
2. Your current emotional state
3. Your relationships with other characters
4. The current game situation
5. Your character's goals and motivations
`;
```

### 5.2 Function Calling for Game Actions

```typescript
const gameFunctions = [
  {
    name: "update_game_state",
    description: "Update the current game state based on player action",
    parameters: {
      type: "object",
      properties: {
        sceneChanges: {
          type: "object",
          description: "Changes to the current scene"
        },
        characterUpdates: {
          type: "array",
          description: "Updates to character states"
        },
        worldStateChanges: {
          type: "object",
          description: "Changes to the world state"
        },
        consequences: {
          type: "array",
          description: "Consequences of the player's action"
        }
      }
    }
  },
  {
    name: "generate_character_dialogue",
    description: "Generate dialogue for one or more characters",
    parameters: {
      type: "object",
      properties: {
        responses: {
          type: "array",
          items: {
            type: "object",
            properties: {
              characterId: { type: "string" },
              dialogue: { type: "string" },
              action: { type: "string" },
              emotion: { type: "string" }
            }
          }
        }
      }
    }
  }
];
```

## 6. Implementation Architecture

### 6.1 Project Structure

```
backend/
├── src/
│   ├── controllers/
│   │   ├── gameController.ts
│   │   ├── characterController.ts
│   │   └── sessionController.ts
│   ├── services/
│   │   ├── gameEngine.ts
│   │   ├── aiService.ts
│   │   ├── characterService.ts
│   │   └── stateManager.ts
│   ├── models/
│   │   ├── Game.ts
│   │   ├── Character.ts
│   │   ├── Scene.ts
│   │   └── Player.ts
│   ├── middleware/
│   │   ├── auth.ts
│   │   ├── validation.ts
│   │   └── rateLimit.ts
│   ├── utils/
│   │   ├── promptBuilder.ts
│   │   ├── responseParser.ts
│   │   └── contextManager.ts
│   ├── config/
│   │   ├── database.ts
│   │   ├── redis.ts
│   │   └── openai.ts
│   └── app.ts
├── tests/
├── docs/
└── package.json
```

### 6.2 Environment Configuration

```typescript
interface Config {
  port: number;
  openaiApiKey: string;
  redisUrl: string;
  databaseUrl: string;
  jwtSecret: string;
  rateLimitWindow: number;
  rateLimitMax: number;
  logLevel: string;
}
```

## 7. Core Implementation Examples

### 7.1 Game Controller

```typescript
import { Request, Response } from 'express';
import { GameEngine } from '../services/gameEngine';
import { validateGameAction } from '../middleware/validation';

export class GameController {
  private gameEngine: GameEngine;

  constructor() {
    this.gameEngine = new GameEngine();
  }

  async createGame(req: Request, res: Response) {
    try {
      const { gameType, playerName, difficulty, customSettings } = req.body;

      const gameSession = await this.gameEngine.createGame({
        gameType,
        playerName,
        difficulty,
        customSettings
      });

      res.status(201).json({
        success: true,
        data: {
          sessionId: gameSession.sessionId,
          gameState: gameSession.initialState,
          message: "Game created successfully"
        }
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  }

  async processAction(req: Request, res: Response) {
    try {
      const { sessionId } = req.params;
      const { action, target, parameters } = req.body;

      const gameResponse = await this.gameEngine.processPlayerAction({
        sessionId,
        action,
        target,
        parameters
      });

      res.json({
        success: true,
        data: gameResponse
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  }

  async getGameState(req: Request, res: Response) {
    try {
      const { sessionId } = req.params;
      const gameState = await this.gameEngine.getGameState(sessionId);

      res.json({
        success: true,
        data: gameState
      });
    } catch (error) {
      res.status(404).json({
        success: false,
        error: "Game session not found"
      });
    }
  }
}
```

### 7.2 AI Service Implementation

```typescript
import OpenAI from 'openai';
import { GameState, GameAction, AIResponse } from '../types';
import { PromptBuilder } from '../utils/promptBuilder';
import { ResponseParser } from '../utils/responseParser';

export class AIService {
  private openai: OpenAI;
  private promptBuilder: PromptBuilder;
  private responseParser: ResponseParser;

  constructor() {
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY
    });
    this.promptBuilder = new PromptBuilder();
    this.responseParser = new ResponseParser();
  }

  async processGameAction(
    userInput: string,
    gameState: GameState
  ): Promise<AIResponse> {
    try {
      const systemPrompt = this.promptBuilder.buildSystemPrompt(gameState);
      const userPrompt = this.promptBuilder.buildUserPrompt(userInput, gameState);

      const completion = await this.openai.chat.completions.create({
        model: "gpt-4",
        messages: [
          { role: "system", content: systemPrompt },
          { role: "user", content: userPrompt }
        ],
        functions: this.getGameFunctions(),
        function_call: "auto",
        temperature: 0.8,
        max_tokens: 1500,
        presence_penalty: 0.1,
        frequency_penalty: 0.1
      });

      return this.responseParser.parseAIResponse(completion);
    } catch (error) {
      throw new Error(`AI processing failed: ${error.message}`);
    }
  }

  async generateCharacterResponse(
    characterId: string,
    context: string,
    gameState: GameState
  ): Promise<string> {
    const character = gameState.characters.find(c => c.id === characterId);
    if (!character) {
      throw new Error(`Character ${characterId} not found`);
    }

    const characterPrompt = this.promptBuilder.buildCharacterPrompt(
      character,
      context,
      gameState
    );

    const completion = await this.openai.chat.completions.create({
      model: "gpt-4",
      messages: [
        { role: "system", content: characterPrompt },
        { role: "user", content: context }
      ],
      temperature: 0.9,
      max_tokens: 300
    });

    return completion.choices[0].message.content || "";
  }

  private getGameFunctions() {
    return [
      {
        name: "update_game_state",
        description: "Update game state based on player action",
        parameters: {
          type: "object",
          properties: {
            stateChanges: {
              type: "object",
              description: "Changes to apply to game state"
            },
            characterResponses: {
              type: "array",
              description: "Responses from game characters"
            },
            narrativeDescription: {
              type: "string",
              description: "Description of what happens"
            },
            availableActions: {
              type: "array",
              description: "New available actions for the player"
            }
          },
          required: ["stateChanges", "narrativeDescription"]
        }
      }
    ];
  }
}
```

### 7.3 Game Engine Core Logic

```typescript
import { AIService } from './aiService';
import { StateManager } from './stateManager';
import { CharacterService } from './characterService';
import { GameState, GameAction, GameResponse } from '../types';

export class GameEngine {
  private aiService: AIService;
  private stateManager: StateManager;
  private characterService: CharacterService;

  constructor() {
    this.aiService = new AIService();
    this.stateManager = new StateManager();
    this.characterService = new CharacterService();
  }

  async createGame(params: CreateGameParams): Promise<GameSession> {
    const gameState = await this.stateManager.initializeGame(params);
    const sessionId = this.generateSessionId();

    await this.stateManager.saveGameState(sessionId, gameState);

    return {
      sessionId,
      initialState: gameState
    };
  }

  async processPlayerAction(action: GameAction): Promise<GameResponse> {
    const gameState = await this.stateManager.getGameState(action.sessionId);

    // Process action through AI
    const aiResponse = await this.aiService.processGameAction(
      action.action,
      gameState
    );

    // Update game state
    const updatedState = await this.stateManager.updateGameState(
      action.sessionId,
      aiResponse.stateChanges
    );

    // Generate character responses
    const characterResponses = await this.characterService.generateResponses(
      action,
      updatedState
    );

    // Save updated state
    await this.stateManager.saveGameState(action.sessionId, updatedState);

    return {
      narrativeDescription: aiResponse.narrativeDescription,
      characterResponses,
      gameState: updatedState,
      availableActions: aiResponse.availableActions || []
    };
  }

  async getGameState(sessionId: string): Promise<GameState> {
    return await this.stateManager.getGameState(sessionId);
  }

  private generateSessionId(): string {
    return `game_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}
```

## 8. Database Schema Design

### 8.1 Game Sessions Collection/Table

```typescript
interface GameSessionDocument {
  _id: string;
  sessionId: string;
  gameType: string;
  playerName: string;
  difficulty: string;
  gameState: GameState;
  createdAt: Date;
  updatedAt: Date;
  isActive: boolean;
  totalActions: number;
}
```

### 8.2 Character Templates Collection/Table

```typescript
interface CharacterTemplate {
  _id: string;
  name: string;
  personality: string;
  background: string;
  defaultMood: string;
  speechPatterns: string[];
  relationships: Record<string, number>;
  gameTypes: string[];
  isNPC: boolean;
}
```

### 8.3 Action History Collection/Table

```typescript
interface ActionHistoryDocument {
  _id: string;
  sessionId: string;
  actionId: string;
  playerAction: string;
  aiResponse: string;
  characterResponses: CharacterResponse[];
  gameStateBefore: GameState;
  gameStateAfter: GameState;
  timestamp: Date;
  processingTime: number;
}
```

## 9. Real-time Features with Socket.io

### 9.1 Socket Event Handlers

```typescript
import { Server } from 'socket.io';
import { GameEngine } from '../services/gameEngine';

export class SocketHandler {
  private io: Server;
  private gameEngine: GameEngine;

  constructor(io: Server) {
    this.io = io;
    this.gameEngine = new GameEngine();
    this.setupEventHandlers();
  }

  private setupEventHandlers() {
    this.io.on('connection', (socket) => {
      console.log(`Client connected: ${socket.id}`);

      socket.on('join-game', async (sessionId: string) => {
        socket.join(sessionId);
        const gameState = await this.gameEngine.getGameState(sessionId);
        socket.emit('game-state-update', gameState);
      });

      socket.on('player-action', async (data) => {
        try {
          const response = await this.gameEngine.processPlayerAction(data);

          // Emit to all clients in the game session
          this.io.to(data.sessionId).emit('game-response', response);

          // Emit character typing indicators
          this.emitTypingIndicators(data.sessionId, response.characterResponses);
        } catch (error) {
          socket.emit('error', { message: error.message });
        }
      });

      socket.on('disconnect', () => {
        console.log(`Client disconnected: ${socket.id}`);
      });
    });
  }

  private async emitTypingIndicators(
    sessionId: string,
    characterResponses: CharacterResponse[]
  ) {
    for (const response of characterResponses) {
      this.io.to(sessionId).emit('character-typing', {
        characterId: response.characterId,
        isTyping: true
      });

      // Simulate typing delay
      setTimeout(() => {
        this.io.to(sessionId).emit('character-response', response);
        this.io.to(sessionId).emit('character-typing', {
          characterId: response.characterId,
          isTyping: false
        });
      }, Math.random() * 2000 + 1000);
    }
  }
}
```

## 10. Security and Rate Limiting

### 10.1 Authentication Middleware

```typescript
import jwt from 'jsonwebtoken';
import { Request, Response, NextFunction } from 'express';

export const authenticateToken = (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ error: 'Access token required' });
  }

  jwt.verify(token, process.env.JWT_SECRET!, (err, user) => {
    if (err) {
      return res.status(403).json({ error: 'Invalid token' });
    }
    req.user = user;
    next();
  });
};
```

### 10.2 Rate Limiting Configuration

```typescript
import rateLimit from 'express-rate-limit';

export const gameActionLimiter = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 30, // 30 actions per minute
  message: {
    error: 'Too many actions. Please wait before trying again.'
  },
  standardHeaders: true,
  legacyHeaders: false
});

export const aiRequestLimiter = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 10, // 10 AI requests per minute
  message: {
    error: 'AI request limit exceeded. Please wait before trying again.'
  }
});
```

## 11. Error Handling and Logging

### 11.1 Global Error Handler

```typescript
import { Request, Response, NextFunction } from 'express';
import { logger } from '../utils/logger';

export const errorHandler = (
  error: Error,
  req: Request,
  res: Response,
  next: NextFunction
) => {
  logger.error('Unhandled error:', {
    error: error.message,
    stack: error.stack,
    url: req.url,
    method: req.method,
    body: req.body,
    timestamp: new Date().toISOString()
  });

  if (error.name === 'ValidationError') {
    return res.status(400).json({
      success: false,
      error: 'Invalid input data',
      details: error.message
    });
  }

  if (error.name === 'OpenAIError') {
    return res.status(503).json({
      success: false,
      error: 'AI service temporarily unavailable'
    });
  }

  res.status(500).json({
    success: false,
    error: 'Internal server error'
  });
};
```

## 12. Deployment and Scaling

### 12.1 Docker Configuration

```dockerfile
FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

EXPOSE 3000

CMD ["npm", "start"]
```

### 12.2 Environment Variables

```bash
# Server Configuration
PORT=3000
NODE_ENV=production

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4

# Database Configuration
DATABASE_URL=mongodb://localhost:27017/gamemaster
REDIS_URL=redis://localhost:6379

# Security
JWT_SECRET=your_jwt_secret_here
CORS_ORIGIN=https://yourdomain.com

# Rate Limiting
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=30

# Logging
LOG_LEVEL=info
LOG_FILE=logs/gamemaster.log
```

## 13. Testing Strategy

### 13.1 Unit Tests Example

```typescript
import { GameEngine } from '../src/services/gameEngine';
import { mockGameState, mockPlayerAction } from './mocks';

describe('GameEngine', () => {
  let gameEngine: GameEngine;

  beforeEach(() => {
    gameEngine = new GameEngine();
  });

  describe('processPlayerAction', () => {
    it('should process valid player action', async () => {
      const result = await gameEngine.processPlayerAction(mockPlayerAction);

      expect(result).toHaveProperty('narrativeDescription');
      expect(result).toHaveProperty('characterResponses');
      expect(result).toHaveProperty('gameState');
      expect(result.characterResponses).toBeInstanceOf(Array);
    });

    it('should handle invalid session ID', async () => {
      const invalidAction = { ...mockPlayerAction, sessionId: 'invalid' };

      await expect(gameEngine.processPlayerAction(invalidAction))
        .rejects.toThrow('Game session not found');
    });
  });
});
```

## 14. Performance Optimization

### 14.1 Caching Strategy

```typescript
import Redis from 'ioredis';

export class CacheService {
  private redis: Redis;

  constructor() {
    this.redis = new Redis(process.env.REDIS_URL);
  }

  async cacheGameState(sessionId: string, gameState: GameState) {
    await this.redis.setex(
      `gamestate:${sessionId}`,
      3600, // 1 hour TTL
      JSON.stringify(gameState)
    );
  }

  async getCachedGameState(sessionId: string): Promise<GameState | null> {
    const cached = await this.redis.get(`gamestate:${sessionId}`);
    return cached ? JSON.parse(cached) : null;
  }

  async cacheCharacterResponse(
    characterId: string,
    context: string,
    response: string
  ) {
    const key = `character:${characterId}:${this.hashContext(context)}`;
    await this.redis.setex(key, 1800, response); // 30 minutes TTL
  }

  private hashContext(context: string): string {
    return require('crypto')
      .createHash('md5')
      .update(context)
      .digest('hex');
  }
}
```

This comprehensive technical documentation provides a complete blueprint for building a sophisticated Game-Master AI Engine Backend that can interpret user actions, manage complex game states, and generate dynamic multi-character responses using the OpenAI SDK and Express.js.
```
