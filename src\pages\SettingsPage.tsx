import React, { useState } from 'react';
import { ArrowLeft, Download, Trash2, Palette, Volume2, VolumeX, Eye, EyeOff } from 'lucide-react';
import { Link } from 'react-router-dom';
import { useChat } from '../contexts/ChatContext';
import { StorageService } from '../utils/storage';
import { toast } from 'sonner';

export function SettingsPage() {
  const { state, updateTheme, updatePreferences, clearHistory } = useChat();
  const { theme, preferences, messages } = state;
  const [showResetConfirm, setShowResetConfirm] = useState(false);

  const accentColors = [
    { name: 'Blue', value: '#007bff' },
    { name: '<PERSON>', value: '#28a745' },
    { name: 'Purple', value: '#6f42c1' },
    { name: 'Red', value: '#dc3545' },
    { name: 'Orange', value: '#fd7e14' },
    { name: '<PERSON><PERSON>', value: '#20c997' },
    { name: 'Pink', value: '#e83e8c' },
    { name: 'Indigo', value: '#6610f2' }
  ];

  const handleThemeChange = (newTheme: Partial<typeof theme>) => {
    updateTheme({ ...theme, ...newTheme });
    toast.success('Theme updated');
  };

  const handlePreferencesChange = (newPreferences: Partial<typeof preferences>) => {
    updatePreferences({ ...preferences, ...newPreferences });
    toast.success('Preferences updated');
  };

  const handleExportChat = () => {
    if (messages.length === 0) {
      toast.info('No messages to export');
      return;
    }

    try {
      const chatData = StorageService.exportChatHistory();
      const blob = new Blob([chatData], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `chat-history-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      toast.success('Chat history exported');
    } catch (error) {
      toast.error('Failed to export chat history');
    }
  };

  const handleClearHistory = () => {
    if (messages.length === 0) {
      toast.info('No messages to clear');
      return;
    }

    clearHistory();
    toast.success('Chat history cleared');
  };

  const handleResetToDefaults = () => {
    if (!showResetConfirm) {
      setShowResetConfirm(true);
      return;
    }

    // Reset to default values
    updateTheme({ mode: 'light', accentColor: '#007bff' });
    updatePreferences({
      autoScroll: true,
      soundNotifications: false,
      compactView: false,
      showTimestamps: true
    });
    
    setShowResetConfirm(false);
    toast.success('Settings reset to defaults');
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors duration-200">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <header className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-4 py-4 md:px-6 md:py-6">
          <div className="flex items-center space-x-4">
            <Link
              to="/"
              className="p-2 rounded-lg text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 hover:bg-gray-100 dark:hover:bg-gray-700 transition-all duration-200"
              title="Back to chat"
            >
              <ArrowLeft className="w-5 h-5" />
            </Link>
            <div>
              <h1 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
                Settings
              </h1>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Customize your chat experience
              </p>
            </div>
          </div>
        </header>

        {/* Settings content */}
        <main className="p-4 md:p-6 space-y-6">
          {/* Theme Settings */}
          <section className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
            <div className="flex items-center space-x-3 mb-4">
              <Palette className="w-5 h-5 text-blue-500" />
              <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                Theme
              </h2>
            </div>

            {/* Dark mode toggle */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    Dark Mode
                  </label>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    Switch between light and dark themes
                  </p>
                </div>
                <button
                  onClick={() => handleThemeChange({ mode: theme.mode === 'light' ? 'dark' : 'light' })}
                  className={`
                    relative inline-flex h-6 w-11 items-center rounded-full transition-colors duration-200
                    ${theme.mode === 'dark' ? 'bg-blue-500' : 'bg-gray-300'}
                  `}
                >
                  <span
                    className={`
                      inline-block h-4 w-4 transform rounded-full bg-white transition-transform duration-200
                      ${theme.mode === 'dark' ? 'translate-x-6' : 'translate-x-1'}
                    `}
                  />
                </button>
              </div>

              {/* Accent color picker */}
              <div>
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3 block">
                  Accent Color
                </label>
                <div className="grid grid-cols-4 md:grid-cols-8 gap-3">
                  {accentColors.map((color) => (
                    <button
                      key={color.value}
                      onClick={() => handleThemeChange({ accentColor: color.value })}
                      className={`
                        w-10 h-10 rounded-lg border-2 transition-all duration-200
                        ${theme.accentColor === color.value 
                          ? 'border-gray-900 dark:border-gray-100 scale-110' 
                          : 'border-gray-300 dark:border-gray-600 hover:scale-105'
                        }
                      `}
                      style={{ backgroundColor: color.value }}
                      title={color.name}
                    />
                  ))}
                </div>
              </div>
            </div>
          </section>

          {/* Chat Preferences */}
          <section className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
              Chat Preferences
            </h2>

            <div className="space-y-4">
              {/* Auto-scroll */}
              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    Auto-scroll
                  </label>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    Automatically scroll to new messages
                  </p>
                </div>
                <input
                  type="checkbox"
                  checked={preferences.autoScroll}
                  onChange={(e) => handlePreferencesChange({ autoScroll: e.target.checked })}
                  className="w-4 h-4 text-blue-500 rounded focus:ring-blue-500"
                />
              </div>

              {/* Sound notifications */}
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  {preferences.soundNotifications ? (
                    <Volume2 className="w-4 h-4 text-gray-500" />
                  ) : (
                    <VolumeX className="w-4 h-4 text-gray-500" />
                  )}
                  <div>
                    <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      Sound Notifications
                    </label>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      Play sounds for message actions
                    </p>
                  </div>
                </div>
                <input
                  type="checkbox"
                  checked={preferences.soundNotifications}
                  onChange={(e) => handlePreferencesChange({ soundNotifications: e.target.checked })}
                  className="w-4 h-4 text-blue-500 rounded focus:ring-blue-500"
                />
              </div>

              {/* Compact view */}
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  {preferences.compactView ? (
                    <EyeOff className="w-4 h-4 text-gray-500" />
                  ) : (
                    <Eye className="w-4 h-4 text-gray-500" />
                  )}
                  <div>
                    <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      Compact View
                    </label>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      Use smaller message bubbles and spacing
                    </p>
                  </div>
                </div>
                <input
                  type="checkbox"
                  checked={preferences.compactView}
                  onChange={(e) => handlePreferencesChange({ compactView: e.target.checked })}
                  className="w-4 h-4 text-blue-500 rounded focus:ring-blue-500"
                />
              </div>

              {/* Show timestamps */}
              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    Show Timestamps
                  </label>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    Display message timestamps
                  </p>
                </div>
                <input
                  type="checkbox"
                  checked={preferences.showTimestamps}
                  onChange={(e) => handlePreferencesChange({ showTimestamps: e.target.checked })}
                  className="w-4 h-4 text-blue-500 rounded focus:ring-blue-500"
                />
              </div>
            </div>
          </section>

          {/* Data Management */}
          <section className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
              Data Management
            </h2>

            <div className="space-y-4">
              {/* Export chat */}
              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    Export Chat History
                  </label>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    Download your messages as JSON
                  </p>
                </div>
                <button
                  onClick={handleExportChat}
                  disabled={messages.length === 0}
                  className={`
                    flex items-center space-x-2 px-4 py-2 rounded-lg transition-all duration-200
                    ${messages.length === 0
                      ? 'bg-gray-100 dark:bg-gray-700 text-gray-400 dark:text-gray-600 cursor-not-allowed'
                      : 'bg-blue-500 hover:bg-blue-600 text-white'
                    }
                  `}
                >
                  <Download className="w-4 h-4" />
                  <span>Export</span>
                </button>
              </div>

              {/* Clear history */}
              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    Clear Chat History
                  </label>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    Delete all messages permanently
                  </p>
                </div>
                <button
                  onClick={handleClearHistory}
                  disabled={messages.length === 0}
                  className={`
                    flex items-center space-x-2 px-4 py-2 rounded-lg transition-all duration-200
                    ${messages.length === 0
                      ? 'bg-gray-100 dark:bg-gray-700 text-gray-400 dark:text-gray-600 cursor-not-allowed'
                      : 'bg-red-500 hover:bg-red-600 text-white'
                    }
                  `}
                >
                  <Trash2 className="w-4 h-4" />
                  <span>Clear</span>
                </button>
              </div>

              {/* Reset to defaults */}
              <div className="flex items-center justify-between pt-4 border-t border-gray-200 dark:border-gray-700">
                <div>
                  <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    Reset to Defaults
                  </label>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    Restore all settings to default values
                  </p>
                </div>
                <button
                  onClick={handleResetToDefaults}
                  className={`
                    px-4 py-2 rounded-lg transition-all duration-200
                    ${showResetConfirm
                      ? 'bg-red-500 hover:bg-red-600 text-white'
                      : 'bg-gray-500 hover:bg-gray-600 text-white'
                    }
                  `}
                >
                  {showResetConfirm ? 'Confirm Reset' : 'Reset'}
                </button>
              </div>
            </div>
          </section>
        </main>
      </div>
    </div>
  );
}